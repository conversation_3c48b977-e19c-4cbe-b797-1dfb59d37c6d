import { AccountTokenModal } from "../modals/AccountTokenModal";
import { useGame } from "@/contexts/GameContext";
import { toast } from "@/hooks/use-toast";
import Header from "./Header";

const Layout = ({ children }) => {
  const { setAccountToken, showAccountConnectModal, setShowAccountConnectModal } = useGame();

  // Handle Account Token submission
  const handleAccountTokenSubmit = (token: string) => {
    setAccountToken(token);
    setShowAccountConnectModal(false);
    toast({
      title: "Account Token Saved",
      description: "Your progress will now be tracked!",
    });
  };

  return (
    <div className="min-h-screen bg-dark-bg">
      <Header />
      {children}

      {/* Account Token Modal */}
      <AccountTokenModal
        isOpen={showAccountConnectModal}
        onClose={() => setShowAccountConnectModal(false)}
        onSubmit={handleAccountTokenSubmit}
      />
    </div>
  );
};

export default Layout;
