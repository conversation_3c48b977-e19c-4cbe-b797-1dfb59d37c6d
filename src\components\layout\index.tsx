import { TelegramIdModal } from "../modals/TelegramIdModal";
import { useGame } from "@/contexts/GameContext";
import { toast } from "@/hooks/use-toast";
import Header from "./Header";

const Layout = ({ children }) => {
  const { setTelegramId, showAccountConnectModal, setShowAccountConnectModal } = useGame();

  // Handle Telegram ID submission
  const handleTelegramIdSubmit = (id: string) => {
    setTelegramId(id);
    setShowAccountConnectModal(false);
    toast({
      title: "Telegram ID Saved",
      description: "Your progress will now be tracked!",
    });
  };

  return (
    <div className="min-h-screen bg-dark-bg">
      <Header />
      {children}

      {/* Telegram ID Modal */}
      <TelegramIdModal
        isOpen={showAccountConnectModal}
        onClose={() => setShowAccountConnectModal(false)}
        onSubmit={handleTelegramIdSubmit}
      />
    </div>
  );
};

export default Layout;
