import React, { createContext, useContext, useState, ReactNode, useEffect } from 'react';
import { Game, UserStats } from '@/types/api';

// Telegram ID storage key
const TELEGRAM_ID_STORAGE_KEY = 'jq_telegram_id';

interface GameContextType {
  // Telegram ID management
  accountToken: string | null;
  setTelegramId: (id: string) => void;
  clearTelegramId: () => void;
  
  // Selected game management
  selectedGame: Game | null;
  setSelectedGame: (game: Game | null) => void;
  
  // User stats for current game
  currentUserStats: UserStats | null;
  setCurrentUserStats: (stats: UserStats | null) => void;
  
  // Modal state for Telegram ID input
  showAccountConnectModal: boolean;
  setShowAccountConnectModal: (show: boolean) => void;
  
  // Helper functions
  isGameSelected: boolean;
  hasTelegramId: boolean;
}

const GameContext = createContext<GameContextType | undefined>(undefined);

export const useGame = () => {
  const context = useContext(GameContext);
  if (context === undefined) {
    throw new Error('useGame must be used within a GameProvider');
  }
  return context;
};

interface GameProviderProps {
  children: ReactNode;
}

export const GameProvider: React.FC<GameProviderProps> = ({ children }) => {
  const [accountToken, setAccountToken] = useState<string | null>(null);
  const [selectedGame, setSelectedGame] = useState<Game | null>(null);
  const [currentUserStats, setCurrentUserStats] = useState<UserStats | null>(null);
  const [showAccountConnectModal, setShowAccountConnectModal] = useState(false);

  // Load Telegram ID from localStorage on mount
  useEffect(() => {
    const storedTelegramId = localStorage.getItem(TELEGRAM_ID_STORAGE_KEY);
    if (storedTelegramId) {
      setAccountToken(storedTelegramId);
    }
  }, []);

  // Set Telegram ID and persist to localStorage
  const setTelegramId = (id: string) => {
    setAccountToken(id);
    localStorage.setItem(TELEGRAM_ID_STORAGE_KEY, id);
  };

  // Clear Telegram ID from state and localStorage
  const clearTelegramId = () => {
    setAccountToken(null);
    localStorage.removeItem(TELEGRAM_ID_STORAGE_KEY);
    setCurrentUserStats(null); // Clear stats when clearing ID
  };

  // Computed values
  const isGameSelected = selectedGame !== null;
  const hasTelegramId = accountToken !== null && accountToken.trim() !== '';

  const value: GameContextType = {
    // Telegram ID management
    accountToken,
    setTelegramId,
    clearTelegramId,
    
    // Selected game management
    selectedGame,
    setSelectedGame,
    
    // User stats for current game
    currentUserStats,
    setCurrentUserStats,
    
    // Modal state
    showAccountConnectModal,
    setShowAccountConnectModal,
    
    // Helper values
    isGameSelected,
    hasTelegramId,
  };

  return (
    <GameContext.Provider value={value}>
      {children}
    </GameContext.Provider>
  );
};
