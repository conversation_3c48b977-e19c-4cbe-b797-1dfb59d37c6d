import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { GameApiService } from '@/services/game-api';
import { Game, UserStats } from '@/types/api';

// Query Keys
export const QUERY_KEYS = {
  GAMES: ['games'] as const,
  USER_STATS: (gameId: string, accountToken: string) => ['user-stats', gameId, accountToken] as const,
} as const;

// Hook to fetch games
export const useGames = () => {
  return useQuery({
    queryKey: QUERY_KEYS.GAMES,
    queryFn: GameApiService.getGames,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes (formerly cacheTime)
  });
};

// Hook to fetch user stats
export const useUserStats = (gameId: string, accountToken: string, enabled: boolean = true) => {
  return useQuery({
    queryKey: QUERY_KEYS.USER_STATS(gameId, accountToken),
    queryFn: () => GameApiService.getUserStats(gameId, accountToken),
    enabled: enabled && !!gameId && !!accountToken,
    staleTime: 30 * 1000, // 30 seconds
    gcTime: 2 * 60 * 1000, // 2 minutes
  });
};

// Hook to update score
export const useUpdateScore = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ gameId, score, accountToken }: { gameId: string; score: number; accountToken: string }) =>
      GameApiService.updateScore(gameId, score, accountToken),
    onSuccess: (data, variables) => {
      // Update the user stats cache with the new data
      queryClient.setQueryData(
        QUERY_KEYS.USER_STATS(variables.gameId, variables.accountToken),
        data
      );
      
      // Optionally invalidate to refetch from server
      queryClient.invalidateQueries({
        queryKey: QUERY_KEYS.USER_STATS(variables.gameId, variables.accountToken),
      });
    },
    onError: (error) => {
      console.error('Failed to update score:', error);
    },
  });
};
