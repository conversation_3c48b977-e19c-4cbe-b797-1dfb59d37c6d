import React, { useState, useEffect } from "react";
import { X, User, AlertCircle, CheckCircle } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useGame } from "@/contexts/GameContext";

interface AccountTokenModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (accountToken: string) => void;
}

export const AccountTokenModal: React.FC<AccountTokenModalProps> = ({
  isOpen,
  onClose,
  onSubmit,
}) => {
  const [accountToken, setAccountToken] = useState("");
  const [error, setError] = useState("");
  const [isValidating, setIsValidating] = useState(false);
  const { accountToken: storedAccountToken } = useGame();

  // Pre-fill with stored Account Token if available
  useEffect(() => {
    if (storedAccountToken) {
      setAccountToken(storedAccountToken);
    }
  }, [storedAccountToken]);

  // Reset form when modal opens/closes
  useEffect(() => {
    if (!isOpen) {
      setError("");
      setIsValidating(false);
    }
  }, [isOpen]);

  // Function to get validation error message
  const getValidationError = (token: string): string => {
    const trimmedToken = token.trim();

    if (!trimmedToken) {
      return "Account Token is required";
    }

    if (trimmedToken.length < 5) {
      return "Account Token must be at least 5 characters long";
    }

    // Check if it's a valid format (can be numeric or username)
    const isNumeric = /^\d+$/.test(trimmedToken);
    const isUsername = /^[a-zA-Z0-9_]{5,32}$/.test(trimmedToken);

    if (!isNumeric && !isUsername) {
      return "Invalid Account Token format. Use your numeric ID or username.";
    }

    return "";
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    const validationError = getValidationError(accountToken);
    if (validationError) {
      setError(validationError);
      return;
    }

    setIsValidating(true);

    try {
      // Here you could add additional validation by calling API
      // For now, we'll just validate the format
      await new Promise((resolve) => setTimeout(resolve, 500)); // Simulate API call

      onSubmit(accountToken.trim());
      onClose();
    } catch (error) {
      setError("Failed to validate Account Token. Please try again.");
    } finally {
      setIsValidating(false);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setAccountToken(value);

    // Clear error when user starts typing
    if (error) {
      setError("");
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      {/* Backdrop */}
      <div
        className="absolute inset-0 bg-black/60 backdrop-blur-sm"
        onClick={onClose}
      />

      {/* Modal */}
      <div className="relative bg-card-dark border border-border-light rounded-xl p-6 w-full max-w-md mx-4 shadow-2xl">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-accent-cyan/10 rounded-lg">
              <User className="h-5 w-5 text-accent-cyan" />
            </div>
            <div>
              <h2 className="text-xl font-bold text-primary-text">
                Connect Account
              </h2>
              <p className="text-sm text-secondary-text">
                Required to track your game progress
              </p>
            </div>
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={onClose}
            className="text-secondary-text hover:text-primary-text"
          >
            <X className="h-4 w-4" />
          </Button>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="account-token" className="text-primary-text">
              Account Token
            </Label>
            <Input
              id="account-token"
              type="text"
              value={accountToken}
              onChange={handleInputChange}
              placeholder="Enter your account token"
              className="bg-surface-dark border-border-light text-primary-text placeholder:text-muted-text"
              disabled={isValidating}
            />

            {/* Error message */}
            {error && (
              <div className="flex items-center space-x-2 text-sm text-red-400">
                <AlertCircle className="h-4 w-4" />
                <span>{error}</span>
              </div>
            )}

            {/* Success message */}
            {!error &&
              accountToken &&
              getValidationError(accountToken) === "" && (
                <div className="flex items-center space-x-2 text-sm text-green-400">
                  <CheckCircle className="h-4 w-4" />
                  <span>Valid Account Token format</span>
                </div>
              )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="account-token" className="text-primary-text">
              Password
            </Label>
            <Input
              id="password"
              type="password"
              value={accountToken}
              onChange={handleInputChange}
              placeholder="En"
              className="bg-surface-dark border-border-light text-primary-text placeholder:text-muted-text"
              disabled={isValidating}
            />

            {/* Error message */}
            {error && (
              <div className="flex items-center space-x-2 text-sm text-red-400">
                <AlertCircle className="h-4 w-4" />
                <span>{error}</span>
              </div>
            )}

            {/* Success message */}
            {!error &&
              accountToken &&
              getValidationError(accountToken) === "" && (
                <div className="flex items-center space-x-2 text-sm text-green-400">
                  <CheckCircle className="h-4 w-4" />
                  <span>Valid Account Token format</span>
                </div>
              )}
          </div>

          {/* Help text */}
          <div className="bg-surface-dark/50 rounded-lg p-3 text-sm text-secondary-text">
            <p className="font-medium mb-1">How to find your Account Token:</p>
            <ul className="space-y-1 text-xs">
              <li>• Message @userinfobot on Telegram</li>
              <li>• Use your username (e.g., @yourname)</li>
              <li>• Or use your numeric ID</li>
            </ul>
          </div>

          {/* Actions */}
          <div className="flex space-x-3 pt-2">
            <Button
              type="button"
              variant="outline"
              onClick={onClose}
              className="flex-1"
              disabled={isValidating}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              className="flex-1 bg-gradient-to-r from-accent-cyan to-xp-purple"
              disabled={isValidating || !accountToken.trim()}
            >
              {isValidating ? "Validating..." : "Continue"}
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
};
