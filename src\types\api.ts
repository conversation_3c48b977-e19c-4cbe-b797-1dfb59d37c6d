// API Response Types
export interface ApiResponse<T> {
  message: string;
  payload: T;
}

// Game Types
export interface Game {
  _id: string;
  game_id: string;
  title: string;
  description: string;
  image: string;
  blockchain: string;
  category: string;
  min_reward: number;
  max_reward: number;
  difficulty: string;
  featured: boolean;
  trending: boolean;
  game_url: string;
}

export interface GamesResponse {
  games: Game[];
}

// User Stats Types
export interface UserStats {
  best_score: number;
  games_played: number;
  total_score: number;
}

export interface UserStatsRequest {
  game_id: string;
  account_token: string;
}

export interface UserStatsResponse extends UserStats {}

// Update Score Types
export interface UpdateScoreRequest {
  game_id: string;
  score: number;
  account_token: string;
}

export interface UpdateScoreResponse extends UserStats {}

// Error Response Type
export interface ApiError {
  error: string;
}

// API Endpoints
export const API_ENDPOINTS = {
  GAMES: "/games",
  USER_STATS: "/user-stats",
  UPDATE_SCORE: "/update-score",
} as const;
